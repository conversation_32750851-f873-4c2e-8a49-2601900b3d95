#include "qrcodedetector.h"
#include <QDir>
#include <QFileInfo>
#include <QCoreApplication>

QRCodeDetector::QRCodeDetector()
    : m_detector(nullptr)
    , m_initialized(false)
{
}

QRCodeDetector::~QRCodeDetector()
{
    if (m_detector) {
        delete m_detector;
        m_detector = nullptr;
    }
}

bool QRCodeDetector::initialize(const QString& modelPath)
{
    try {
        // 清理之前的检测器
        if (m_detector) {
            delete m_detector;
            m_detector = nullptr;
        }
        
        m_initialized = false;
        m_lastError.clear();
        
        // 检查模型文件路径
        QDir modelDir(modelPath);
        if (!modelDir.exists()) {
            setError(QString("Model directory does not exist: %1").arg(modelPath));
            return false;
        }
        
        // 构建模型文件路径
        QString detectProto = modelDir.absoluteFilePath("detect.prototxt");
        QString detectModel = modelDir.absoluteFilePath("detect.caffemodel");
        QString srProto = modelDir.absoluteFilePath("sr.prototxt");
        QString srModel = modelDir.absoluteFilePath("sr.caffemodel");
        
        // 检查所有模型文件是否存在
        QStringList modelFiles = {detectProto, detectModel, srProto, srModel};
        for (const QString& file : modelFiles) {
            if (!QFileInfo::exists(file)) {
                setError(QString("Model file does not exist: %1").arg(file));
                return false;
            }
        }
        
        qDebug() << "Initializing WeChat QR code detector...";
        qDebug() << "Detection model file:" << detectProto;
        qDebug() << "Detection weight file:" << detectModel;
        qDebug() << "Super resolution model file:" << srProto;
        qDebug() << "Super resolution weight file:" << srModel;
        
        // 创建微信二维码检测器
        m_detector = new cv::wechat_qrcode::WeChatQRCode(
            detectProto.toStdString(),
            detectModel.toStdString(),
            srProto.toStdString(),
            srModel.toStdString()
        );
        
        if (!m_detector) {
            setError("Failed to create WeChat QR code detector");
            return false;
        }
        
        m_initialized = true;
        qDebug() << "WeChat QR code detector initialized successfully";
        return true;
        
    } catch (const cv::Exception& e) {
        setError(QString("OpenCV exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "OpenCV exception:" << e.what();
        return false;
    } catch (const std::exception& e) {
        setError(QString("Standard exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "Standard exception:" << e.what();
        return false;
    } catch (...) {
        setError("Unknown exception");
        qWarning() << "Unknown exception";
        return false;
    }
}

QStringList QRCodeDetector::detectQRCodes(const QString& imagePath)
{
    QStringList results;
    
    if (!m_initialized) {
        setError("Detector not initialized");
        return results;
    }
    
    if (!QFileInfo::exists(imagePath)) {
        setError(QString("Image file does not exist: %1").arg(imagePath));
        return results;
    }
    
    try {
        // 读取图片
        cv::Mat image = cv::imread(imagePath.toStdString());
        if (image.empty()) {
            setError(QString("Unable to read image: %1").arg(imagePath));
            return results;
        }
        
        qDebug() << "Starting QR code detection, image:" << imagePath;
        qDebug() << "Image size:" << image.cols << "x" << image.rows;
        
        return detectQRCodes(image);
        
    } catch (const cv::Exception& e) {
        setError(QString("OpenCV exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "OpenCV exception occurred during QR code detection:" << e.what();
        return results;
    } catch (const std::exception& e) {
        setError(QString("Standard exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "Standard exception occurred during QR code detection:" << e.what();
        return results;
    } catch (...) {
        setError("Unknown exception occurred during QR code detection");
        qWarning() << "Unknown exception occurred during QR code detection";
        return results;
    }
}

QStringList QRCodeDetector::detectQRCodes(const cv::Mat& image)
{
    QStringList results;
    
    if (!m_initialized) {
        setError("Detector not initialized");
        return results;
    }
    
    if (image.empty()) {
        setError("Input image is empty");
        return results;
    }
    
    try {
        std::vector<cv::Mat> points;
        std::vector<std::string> texts;
        
        // 执行二维码检测
        texts = m_detector->detectAndDecode(image, points);
        
        qDebug() << "Detected" << texts.size() << "QR codes";
        
        // 转换结果
        for (size_t i = 0; i < texts.size(); ++i) {
            QString qrText = QString::fromStdString(texts[i]);
            if (!qrText.isEmpty()) {
                results.append(qrText);
                qDebug() << "QR code" << (i + 1) << "content:" << qrText;

                // Print QR code position information
                if (i < points.size() && !points[i].empty()) {
                    qDebug() << "QR code" << (i + 1) << "position points:" << points[i].rows;
                }
            }
        }
        
        if (results.isEmpty()) {
            qDebug() << "No valid QR code content detected";
        }
        
        m_lastError.clear();
        return results;
        
    } catch (const cv::Exception& e) {
        setError(QString("OpenCV exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "OpenCV exception occurred during QR code detection:" << e.what();
        return results;
    } catch (const std::exception& e) {
        setError(QString("Standard exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "Standard exception occurred during QR code detection:" << e.what();
        return results;
    } catch (...) {
        setError("Unknown exception occurred during QR code detection");
        qWarning() << "Unknown exception occurred during QR code detection";
        return results;
    }
}

void QRCodeDetector::setError(const QString& error)
{
    m_lastError = error;
    qWarning() << "QRCodeDetector error:" << error;
}
