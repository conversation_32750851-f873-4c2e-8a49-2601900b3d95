#!/usr/bin/env python3
"""
跨平台OpenCV下载脚本
支持macOS和Windows平台，自动下载OpenCV预编译库到项目本地目录
"""

import os
import sys
import platform
import urllib.request
import zipfile
import shutil
import subprocess
from pathlib import Path

# OpenCV版本配置
OPENCV_VERSION = "4.8.0"
PROJECT_ROOT = Path(__file__).parent.parent
OPENCV_DIR = PROJECT_ROOT / "third_party" / "opencv"

# 下载配置 - 需要从源码编译以包含contrib模块
DOWNLOAD_CONFIGS = {
    "Windows": {
        # 下载OpenCV源码和contrib模块源码
        "opencv_url": f"https://github.com/opencv/opencv/archive/refs/tags/{OPENCV_VERSION}.zip",
        "opencv_filename": f"opencv-{OPENCV_VERSION}.zip",
        "contrib_url": f"https://github.com/opencv/opencv_contrib/archive/refs/tags/{OPENCV_VERSION}.zip",
        "contrib_filename": f"opencv_contrib-{OPENCV_VERSION}.zip",
        "build_from_source": True,
        "extract_method": "zip_extract"
    },
    "Darwin": {
        # macOS使用Homebrew或conda-forge安装
        "use_package_manager": True,
        "methods": ["homebrew", "conda", "manual_build"],
        "fallback_brew": True
    }
}

def get_platform_info():
    """获取平台信息"""
    system = platform.system()
    machine = platform.machine()
    
    print(f"检测到系统: {system} {machine}")
    
    if system not in DOWNLOAD_CONFIGS:
        raise RuntimeError(f"不支持的操作系统: {system}")
    
    return system, machine

def download_file(url, filename, chunk_size=8192):
    """下载文件并显示进度"""
    print(f"正在下载: {url}")
    
    try:
        with urllib.request.urlopen(url) as response:
            total_size = int(response.headers.get('Content-Length', 0))
            downloaded = 0
            
            with open(filename, 'wb') as f:
                while True:
                    chunk = response.read(chunk_size)
                    if not chunk:
                        break
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}%", end="", flush=True)
            
            print(f"\n下载完成: {filename}")
            return True
            
    except Exception as e:
        print(f"\n下载失败: {e}")
        return False

def extract_zip(zip_path, extract_to):
    """解压ZIP文件"""
    print(f"正在解压: {zip_path}")
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print("解压完成")
        return True
    except Exception as e:
        print(f"解压失败: {e}")
        return False

def extract_exe(exe_path, extract_to):
    """解压Windows自解压文件"""
    print(f"正在解压: {exe_path}")
    try:
        # 使用静默模式解压
        cmd = [str(exe_path), "-o" + str(extract_to), "-y"]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("解压完成")
            return True
        else:
            print(f"解压失败: {result.stderr}")
            return False

    except Exception as e:
        print(f"解压失败: {e}")
        return False

def extract_tar_gz(tar_path, extract_to):
    """解压tar.gz文件"""
    print(f"正在解压: {tar_path}")
    try:
        import tarfile
        with tarfile.open(tar_path, 'r:gz') as tar_ref:
            tar_ref.extractall(extract_to)
        print("解压完成")
        return True
    except Exception as e:
        print(f"解压失败: {e}")
        return False

def install_opencv_macos():
    """在macOS上安装OpenCV"""
    print("在macOS上安装OpenCV...")
    print("尝试多种安装方法...")

    # 方法1: 尝试Homebrew
    if install_opencv_with_brew():
        return True

    # 方法2: 尝试conda
    if install_opencv_with_conda():
        return True

    # 方法3: 提供手动安装指导
    print_manual_install_instructions()
    return False

def install_opencv_with_brew():
    """使用Homebrew安装OpenCV"""
    print("\n方法1: 尝试使用Homebrew安装OpenCV...")
    try:
        # 检查brew是否可用
        subprocess.run(["brew", "--version"], check=True, capture_output=True)

        # 安装OpenCV
        print("正在通过Homebrew安装OpenCV...")
        result = subprocess.run(["brew", "install", "opencv"], capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ Homebrew安装成功")
            # 创建符号链接到我们的目录
            return create_symlinks_from_brew()
        else:
            print(f"❌ Homebrew安装失败: {result.stderr}")
            return False

    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Homebrew不可用，请先安装Homebrew: https://brew.sh")
        return False

def install_opencv_with_conda():
    """使用conda安装OpenCV"""
    print("\n方法2: 尝试使用conda安装OpenCV...")
    try:
        # 检查conda是否可用
        subprocess.run(["conda", "--version"], check=True, capture_output=True)

        # 使用conda-forge安装OpenCV
        print("正在通过conda安装OpenCV...")
        result = subprocess.run([
            "conda", "install", "-c", "conda-forge", "opencv", "-y"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ conda安装成功")
            # 查找conda环境中的OpenCV
            return create_symlinks_from_conda()
        else:
            print(f"❌ conda安装失败: {result.stderr}")
            return False

    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ conda不可用")
        return False

def create_symlinks_from_brew():
    """从Homebrew安装创建符号链接"""
    try:
        # 查找Homebrew OpenCV安装路径
        brew_prefixes = ["/opt/homebrew", "/usr/local"]
        brew_opencv_path = None

        for prefix in brew_prefixes:
            opencv_cellar = Path(prefix) / "Cellar" / "opencv"
            if opencv_cellar.exists():
                # 找到最新版本
                versions = [d for d in opencv_cellar.iterdir() if d.is_dir()]
                if versions:
                    brew_opencv_path = sorted(versions)[-1]
                    break

        if not brew_opencv_path:
            print("❌ 未找到Homebrew OpenCV安装")
            return False

        print(f"找到Homebrew OpenCV: {brew_opencv_path}")

        # 创建目录
        OPENCV_DIR.mkdir(parents=True, exist_ok=True)

        # 创建include符号链接，处理OpenCV 4.x的新目录结构
        opencv4_include = brew_opencv_path / "include" / "opencv4"
        traditional_include = brew_opencv_path / "include"
        include_dst = OPENCV_DIR / "include"

        if opencv4_include.exists():
            # OpenCV 4.x 新结构
            if include_dst.exists():
                include_dst.unlink()
            include_dst.symlink_to(opencv4_include)
            print(f"✅ 创建include符号链接 (opencv4): {opencv4_include}")
        elif traditional_include.exists():
            # 传统结构
            if include_dst.exists():
                include_dst.unlink()
            include_dst.symlink_to(traditional_include)
            print(f"✅ 创建include符号链接: {traditional_include}")
        else:
            print("❌ 未找到include目录")
            return False

        # 创建lib符号链接
        lib_src = brew_opencv_path / "lib"
        lib_dst = OPENCV_DIR / "lib"
        if lib_src.exists():
            if lib_dst.exists():
                lib_dst.unlink()
            lib_dst.symlink_to(lib_src)
            print(f"✅ 创建lib符号链接: {lib_src}")

        return True

    except Exception as e:
        print(f"❌ 创建符号链接失败: {e}")
        return False

def create_symlinks_from_conda():
    """从conda安装创建符号链接"""
    try:
        # 获取conda环境路径
        result = subprocess.run(["conda", "info", "--base"],
                              capture_output=True, text=True, check=True)
        conda_base = Path(result.stdout.strip())

        # 查找OpenCV安装
        possible_paths = [
            conda_base / "lib" / "python3.9" / "site-packages" / "cv2",
            conda_base / "lib" / "python3.10" / "site-packages" / "cv2",
            conda_base / "lib" / "python3.11" / "site-packages" / "cv2",
        ]

        opencv_path = None
        for path in possible_paths:
            if path.exists():
                opencv_path = path
                break

        if not opencv_path:
            print("❌ 未找到conda OpenCV安装")
            return False

        print(f"找到conda OpenCV: {opencv_path}")

        # 这里需要更复杂的逻辑来处理conda的OpenCV
        # 暂时返回False，让用户使用其他方法
        print("⚠️  conda OpenCV需要额外配置，建议使用Homebrew")
        return False

    except Exception as e:
        print(f"❌ conda符号链接失败: {e}")
        return False

def print_manual_install_instructions():
    """打印手动安装说明"""
    print("\n" + "="*60)
    print("自动安装失败，请手动安装OpenCV")
    print("="*60)
    print()
    print("方法1: 使用Homebrew (推荐)")
    print("  1. 安装Homebrew: https://brew.sh")
    print("  2. 运行: brew install opencv")
    print("  3. 重新运行此脚本")
    print()
    print("方法2: 使用MacPorts")
    print("  1. 安装MacPorts: https://www.macports.org")
    print("  2. 运行: sudo port install opencv4")
    print()
    print("方法3: 从源码编译")
    print("  1. 下载OpenCV源码: https://opencv.org/releases/")
    print("  2. 使用CMake编译并安装到 third_party/opencv/")
    print()
    print("安装完成后，请重新运行构建脚本")
    print("="*60)

def build_opencv_from_source_windows(config):
    """Windows平台从源码编译OpenCV"""
    print("开始从源码编译OpenCV (包含contrib模块)...")

    # 下载OpenCV源码
    opencv_zip = OPENCV_DIR / config["opencv_filename"]
    if not opencv_zip.exists():
        print("下载OpenCV源码...")
        if not download_file(config["opencv_url"], opencv_zip):
            print("下载OpenCV源码失败")
            return False

    # 下载OpenCV contrib源码
    contrib_zip = OPENCV_DIR / config["contrib_filename"]
    if not contrib_zip.exists():
        print("下载OpenCV contrib源码...")
        if not download_file(config["contrib_url"], contrib_zip):
            print("下载OpenCV contrib源码失败")
            return False

    # 解压源码
    opencv_src_dir = OPENCV_DIR / f"opencv-{OPENCV_VERSION}"
    contrib_src_dir = OPENCV_DIR / f"opencv_contrib-{OPENCV_VERSION}"

    if not opencv_src_dir.exists():
        print("解压OpenCV源码...")
        if not extract_zip(opencv_zip, OPENCV_DIR):
            return False

    if not contrib_src_dir.exists():
        print("解压OpenCV contrib源码...")
        if not extract_zip(contrib_zip, OPENCV_DIR):
            return False

    # 创建构建目录
    build_dir = opencv_src_dir / "build"
    build_dir.mkdir(exist_ok=True)

    # 查找MinGW编译器
    mingw_path = find_mingw_compiler()
    if not mingw_path:
        print("未找到MinGW编译器")
        return False

    # 查找CMake
    cmake_path = find_cmake()
    if not cmake_path:
        print("未找到CMake")
        return False

    # 配置CMake
    print("配置CMake...")
    cmake_cmd = [
        str(cmake_path),
        "-G", "MinGW Makefiles",
        f"-DCMAKE_C_COMPILER={mingw_path}/gcc.exe",
        f"-DCMAKE_CXX_COMPILER={mingw_path}/g++.exe",
        f"-DCMAKE_INSTALL_PREFIX={OPENCV_DIR}/opencv",
        f"-DOPENCV_EXTRA_MODULES_PATH={contrib_src_dir}/modules",
        "-DBUILD_EXAMPLES=OFF",
        "-DBUILD_TESTS=OFF",
        "-DBUILD_PERF_TESTS=OFF",
        "-DWITH_QT=OFF",
        "-DWITH_GTK=OFF",
        "-DWITH_OPENGL=ON",
        "-DWITH_OPENCL=ON",
        "-DBUILD_opencv_world=OFF",
        ".."
    ]

    try:
        result = subprocess.run(cmake_cmd, cwd=build_dir, check=True, capture_output=True, text=True)
        print("CMake配置成功")
    except subprocess.CalledProcessError as e:
        print(f"CMake配置失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

    # 编译
    print("开始编译OpenCV (这可能需要很长时间)...")
    make_cmd = [str(mingw_path / "mingw32-make.exe"), "-j4"]

    try:
        result = subprocess.run(make_cmd, cwd=build_dir, check=True, capture_output=True, text=True)
        print("编译成功")
    except subprocess.CalledProcessError as e:
        print(f"编译失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

    # 安装
    print("安装OpenCV...")
    install_cmd = [str(mingw_path / "mingw32-make.exe"), "install"]

    try:
        result = subprocess.run(install_cmd, cwd=build_dir, check=True, capture_output=True, text=True)
        print("安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def find_mingw_compiler():
    """查找MinGW编译器"""
    # 常见的MinGW路径
    mingw_paths = [
        Path("D:/Qt/Tools/mingw1310_64/bin"),
        Path("C:/Qt/Tools/mingw1310_64/bin"),
        Path("D:/Qt/Tools/mingw1120_64/bin"),
        Path("C:/Qt/Tools/mingw1120_64/bin"),
        Path("C:/MinGW/bin"),
        Path("D:/MinGW/bin"),
    ]

    for path in mingw_paths:
        if path.exists() and (path / "gcc.exe").exists():
            print(f"找到MinGW编译器: {path}")
            return path

    return None

def find_cmake():
    """查找CMake"""
    # 常见的CMake路径
    cmake_paths = [
        Path("D:/Qt/Tools/CMake_64/bin/cmake.exe"),
        Path("C:/Qt/Tools/CMake_64/bin/cmake.exe"),
        Path("C:/Program Files/CMake/bin/cmake.exe"),
        Path("C:/Program Files (x86)/CMake/bin/cmake.exe"),
        Path("C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe"),
    ]

    for path in cmake_paths:
        if path.exists():
            print(f"找到CMake: {path}")
            return path

    return None

def extract_zip(zip_path, extract_to):
    """解压ZIP文件"""
    try:
        import zipfile
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print(f"解压成功: {zip_path}")
        return True
    except Exception as e:
        print(f"解压失败: {e}")
        return False

def verify_opencv_installation(system):
    """验证OpenCV安装"""
    if system == "Windows":
        # Windows验证
        opencv_root = OPENCV_DIR / "opencv"

        # 检查基本的include目录
        include_path = opencv_root / "build" / "include" / "opencv2" / "opencv.hpp"
        if not include_path.exists():
            print(f"验证失败: 缺少 {include_path}")
            return False

        # 检查x64目录下的Visual Studio版本
        x64_dir = opencv_root / "build" / "x64"
        if not x64_dir.exists():
            print(f"验证失败: 缺少 {x64_dir}")
            return False

        # 查找可用的VC版本 (vc14, vc15, vc16等)
        vc_dirs = [d for d in x64_dir.iterdir() if d.is_dir() and d.name.startswith('vc')]
        if not vc_dirs:
            print(f"验证失败: 在 {x64_dir} 中未找到任何VC版本目录")
            return False

        # 使用找到的第一个VC版本进行验证
        vc_dir = vc_dirs[0]
        print(f"找到Visual Studio版本: {vc_dir.name}")

        required_paths = [
            include_path,
            vc_dir / "lib",
            vc_dir / "bin"
        ]
    else:
        # macOS验证
        required_paths = [
            OPENCV_DIR / "include" / "opencv2" / "opencv.hpp",
            OPENCV_DIR / "lib"
        ]

    for path in required_paths:
        if not path.exists():
            print(f"验证失败: 缺少 {path}")
            return False

    print("OpenCV安装验证成功!")
    return True

def main():
    """主函数"""
    print("OpenCV跨平台下载脚本")
    print("=" * 50)
    
    try:
        # 获取平台信息
        system, _ = get_platform_info()
        config = DOWNLOAD_CONFIGS[system]

        # 创建目录
        OPENCV_DIR.mkdir(parents=True, exist_ok=True)

        # 检查是否已经安装
        if verify_opencv_installation(system):
            print("OpenCV已经安装，跳过下载")
            return 0

        # 根据平台处理
        if system == "Windows":
            if config.get("build_from_source", False):
                # Windows: 从源码编译
                if not build_opencv_from_source_windows(config):
                    print("从源码编译OpenCV失败")
                    return 1
            else:
                # Windows: 下载预编译版本
                download_path = OPENCV_DIR / config["filename"]

                if not download_path.exists():
                    if not download_file(config["url"], download_path):
                        print("下载失败，请检查网络连接")
                        return 1

                # 解压Windows exe文件
                if not extract_exe(download_path, OPENCV_DIR):
                    return 1

        elif system == "Darwin":
            # macOS: 使用包管理器或手动安装
            if not install_opencv_macos():
                print("macOS OpenCV安装失败")
                return 1
        
        # 验证安装
        if not verify_opencv_installation(system):
            print("OpenCV安装验证失败")
            return 1

        # 清理下载文件（仅Windows）
        if system == "Windows":
            download_path = OPENCV_DIR / config["filename"]
            if download_path.exists():
                download_path.unlink()
                print(f"已清理下载文件: {download_path}")

        print("\nOpenCV安装完成!")
        print(f"安装路径: {OPENCV_DIR}")

        return 0
        
    except Exception as e:
        print(f"错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
